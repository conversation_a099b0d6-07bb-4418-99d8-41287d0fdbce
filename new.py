import sys
import os
import time
import random
import threading
import queue
from datetime import datetime
import numpy as np
import cv2
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                            QLabel, QFileDialog, QProgressBar, QTabWidget, QSplitter,
                            QGroupBox, QGridLayout, QTextEdit, QFrame, QComboBox, QPushButton, QSlider)
from PyQt5.QtGui import QPixmap, QImage, QFont, QPainter, QPen, QColor
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer

# YOLO模型模拟
class YOLOModel:
    def __init__(self, model_type):
        self.model_type = model_type
        self.classes = ['person', 'car', 'truck', 'bicycle', 'motorcycle', 'bus']
        
    def detect(self, image, confidence_threshold=0.5):
        # 模拟检测过程
        height, width = image.shape[:2]
        num_objects = random.randint(1, 5)
        detections = []
        
        for _ in range(num_objects):
            x1 = random.randint(0, width - 50)
            y1 = random.randint(0, height - 50)
            x2 = random.randint(x1 + 20, width)
            y2 = random.randint(y1 + 20, height)
            
            obj_class = random.choice(self.classes)
            confidence = random.uniform(0.4, 0.95)
            
            if confidence >= confidence_threshold:
                detections.append({
                    'class': obj_class,
                    'confidence': confidence,
                    'box': [x1, y1, x2, y2]
                })
        
        # 模拟处理时间
        time.sleep(0.5)
        return detections

# 特征提取器
class FeatureExtractor:
    def __init__(self):
        pass
    
    def extract_spatial_features(self, image):
        # 模拟空域特征提取
        return {
            'contrast': round(random.uniform(0.3, 0.9), 2),
            'entropy': round(random.uniform(4.0, 7.0), 2),
            'homogeneity': round(random.uniform(0.6, 0.95), 2)
        }
    
    def extract_frequency_features(self, image):
        # 模拟频域特征提取
        return {
            'low_freq_energy': round(random.uniform(0.4, 0.8), 2),
            'high_freq_energy': round(random.uniform(0.1, 0.5), 2),
            'dominant_frequency': round(random.uniform(10, 50), 2)
        }
    
    def extract_wavelet_features(self, image):
        # 模拟小波域特征提取
        return {
            'approximation': round(random.uniform(0.5, 0.9), 2),
            'horizontal': round(random.uniform(0.1, 0.4), 2),
            'vertical': round(random.uniform(0.1, 0.4), 2),
            'diagonal': round(random.uniform(0.1, 0.3), 2)
        }
    
    def extract_gradient_features(self, image):
        # 模拟梯度特征提取
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY) if len(image.shape) == 3 else image
        sobelx = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
        sobely = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
        magnitude = np.sqrt(sobelx**2 + sobely**2)
        magnitude = cv2.normalize(magnitude, None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)
        return magnitude

# 场景识别器
class SceneRecognizer:
    def __init__(self):
        pass
    
    def recognize(self, visible_image, infrared_image=None):
        # 模拟场景识别
        if infrared_image is None:
            # 只有可见光图像，难以准确判断
            return "无法判断", "缺少红外图像数据"
        
        # 提取特征差异
        visible_features = FeatureExtractor().extract_spatial_features(visible_image)
        infrared_features = FeatureExtractor().extract_spatial_features(infrared_image)
        
        # 模拟差异分析
        contrast_diff = abs(visible_features['contrast'] - infrared_features['contrast'])
        entropy_diff = abs(visible_features['entropy'] - infrared_features['entropy'])
        
        if contrast_diff > 0.3 and entropy_diff > 1.0:
            scene = "黑夜"
            reason = f"因为可见光图像对比度为{visible_features['contrast']}、红外图像对比度为{infrared_features['contrast']}，差异为{contrast_diff:.2f}；可见光图像熵为{visible_features['entropy']}、红外图像熵为{infrared_features['entropy']}，差异为{entropy_diff:.2f}，所以判断为黑夜"
        else:
            scene = "白天"
            reason = f"因为可见光图像对比度为{visible_features['contrast']}、红外图像对比度为{infrared_features['contrast']}，差异为{contrast_diff:.2f}；可见光图像熵为{visible_features['entropy']}、红外图像熵为{infrared_features['entropy']}，差异为{entropy_diff:.2f}，所以判断为白天"
        
        return scene, reason

# 图像处理工作线程
class ProcessingThread(QThread):
    progress_updated = pyqtSignal(int, str)
    feature_extracted = pyqtSignal(dict)
    scene_recognized = pyqtSignal(str, str)
    detection_updated = pyqtSignal(list, list)
    processing_complete = pyqtSignal()
    
    def __init__(self, visible_image, infrared_image=None, sar_image=None, video_path=None):
        super().__init__()
        self.visible_image = visible_image
        self.infrared_image = infrared_image
        self.sar_image = sar_image
        self.video_path = video_path
        self.feature_extractor = FeatureExtractor()
        self.scene_recognizer = SceneRecognizer()
        self.yolo_models = {
            'visible': YOLOModel('visible'),
            'infrared': YOLOModel('infrared'),
            'sar': YOLOModel('sar')
        }
        self.is_running = True
    
    def run(self):
        try:
            # 1. 特征提取
            self.progress_updated.emit(10, "正在提取空域特征...")
            spatial_features = self.feature_extractor.extract_spatial_features(self.visible_image)
            time.sleep(0.5)
            
            self.progress_updated.emit(25, "正在提取频域特征...")
            frequency_features = self.feature_extractor.extract_frequency_features(self.visible_image)
            time.sleep(0.5)
            
            self.progress_updated.emit(40, "正在提取小波域特征...")
            wavelet_features = self.feature_extractor.extract_wavelet_features(self.visible_image)
            time.sleep(0.5)
            
            self.feature_extracted.emit({
                'spatial': spatial_features,
                'frequency': frequency_features,
                'wavelet': wavelet_features
            })
            
            # 2. 场景识别
            self.progress_updated.emit(50, "正在进行场景识别...")
            scene, reason = self.scene_recognizer.recognize(self.visible_image, self.infrared_image)
            time.sleep(0.5)
            self.scene_recognized.emit(scene, reason)
            
            # 3. 选择探测体制和模型
            self.progress_updated.emit(60, "正在选择探测体制和模型...")
            detection_systems = ['可见光探测', '红外探测']
            strategy = "特征融合识别策略"
            
            # 根据场景选择模型权重
            if scene == "白天":
                model_weights = {'visible': 0.7, 'infrared': 0.3}
            else:  # 黑夜
                model_weights = {'visible': 0.3, 'infrared': 0.7}
            
            time.sleep(0.5)
            
            # 4. 目标检测
            self.progress_updated.emit(70, "正在进行目标检测...")
            
            # 对可见光图像进行检测
            visible_detections = self.yolo_models['visible'].detect(self.visible_image)
            
            # 对红外图像进行检测
            infrared_detections = []
            if self.infrared_image is not None:
                infrared_detections = self.yolo_models['infrared'].detect(self.infrared_image)
            
            # 5. 结果调整
            self.progress_updated.emit(85, "正在调整识别结果...")
            
            # 调整前的检测结果（偏移锚框，降低置信度）
            adjusted_before_visible = self._adjust_detections(visible_detections, offset=10, confidence_factor=0.8)
            adjusted_before_infrared = []
            if self.infrared_image is not None:
                adjusted_before_infrared = self._adjust_detections(infrared_detections, offset=10, confidence_factor=0.8)
            
            # 调整后的检测结果（恢复正常）
            adjusted_after_visible = self._adjust_detections(visible_detections, offset=0, confidence_factor=1.0)
            adjusted_after_infrared = []
            if self.infrared_image is not None:
                adjusted_after_infrared = self._adjust_detections(infrared_detections, offset=0, confidence_factor=1.2)
            
            time.sleep(0.5)
            
            # 6. 发送检测结果
            self.detection_updated.emit(
                [adjusted_before_visible, adjusted_after_visible],
                [adjusted_before_infrared, adjusted_after_infrared]
            )
            
            self.progress_updated.emit(100, "处理完成")
            time.sleep(0.5)
            self.processing_complete.emit()
            
        except Exception as e:
            print(f"处理线程错误: {str(e)}")
    
    def _adjust_detections(self, detections, offset=0, confidence_factor=1.0):
        adjusted = []
        for det in detections:
            x1, y1, x2, y2 = det['box']
            adjusted_box = [x1 + offset, y1 + offset, x2 + offset, y2 + offset]
            adjusted_confidence = min(1.0, det['confidence'] * confidence_factor)
            
            adjusted.append({
                'class': det['class'],
                'confidence': adjusted_confidence,
                'box': adjusted_box
            })
        
        return adjusted
    
    def stop(self):
        self.is_running = False
        self.quit()
        self.wait()

# 主窗口
class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.visible_image = None
        self.infrared_image = None
        self.sar_image = None
        self.visible_video_path = None
        self.infrared_video_path = None
        self.processing_thread = None
        self.current_scene = None
        self.is_video_analysis = False
        self.video_timer = QTimer()
        self.video_timer.timeout.connect(self.process_video_frame)
        
        self.init_ui()
    
    def init_ui(self):
        self.setWindowTitle('多模态图像分析软件')
        self.setGeometry(100, 100, 1200, 800)
        
        # 主布局
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # 模式选择
        self.mode_combo = QComboBox()
        self.mode_combo.addItems(["图像分析", "视频分析"])
        self.mode_combo.currentIndexChanged.connect(self.change_mode)
        
        mode_layout = QHBoxLayout()
        mode_layout.addWidget(QLabel("分析模式:"))
        mode_layout.addWidget(self.mode_combo)
        main_layout.addLayout(mode_layout)
        
        # 文件选择区域
        self.file_selection_group = QGroupBox("文件选择")
        self.file_selection_layout = QGridLayout(self.file_selection_group)
        
        self.visible_path_label = QLabel("未选择可见光图像")
        self.infrared_path_label = QLabel("未选择红外图像")
        self.sar_path_label = QLabel("未选择SAR图像")
        self.visible_video_label = QLabel("未选择可见光视频")
        self.infrared_video_label = QLabel("未选择红外视频")
        
        visible_btn = QLabel("点击选择可见光图像")
        visible_btn.setAlignment(Qt.AlignCenter)
        visible_btn.setStyleSheet("background-color: #e0e0e0; border: 1px solid #a0a0a0; padding: 5px;")
        visible_btn.setCursor(Qt.PointingHandCursor)
        visible_btn.mousePressEvent = lambda event: self.select_file("visible")
        
        infrared_btn = QLabel("点击选择红外图像")
        infrared_btn.setAlignment(Qt.AlignCenter)
        infrared_btn.setStyleSheet("background-color: #e0e0e0; border: 1px solid #a0a0a0; padding: 5px;")
        infrared_btn.setCursor(Qt.PointingHandCursor)
        infrared_btn.mousePressEvent = lambda event: self.select_file("infrared")
        
        sar_btn = QLabel("点击选择SAR图像")
        sar_btn.setAlignment(Qt.AlignCenter)
        sar_btn.setStyleSheet("background-color: #e0e0e0; border: 1px solid #a0a0a0; padding: 5px;")
        sar_btn.setCursor(Qt.PointingHandCursor)
        sar_btn.mousePressEvent = lambda event: self.select_file("sar")
        
        visible_video_btn = QLabel("点击选择可见光视频")
        visible_video_btn.setAlignment(Qt.AlignCenter)
        visible_video_btn.setStyleSheet("background-color: #e0e0e0; border: 1px solid #a0a0a0; padding: 5px;")
        visible_video_btn.setCursor(Qt.PointingHandCursor)
        visible_video_btn.mousePressEvent = lambda event: self.select_file("visible_video")
        
        infrared_video_btn = QLabel("点击选择红外视频")
        infrared_video_btn.setAlignment(Qt.AlignCenter)
        infrared_video_btn.setStyleSheet("background-color: #e0e0e0; border: 1px solid #a0a0a0; padding: 5px;")
        infrared_video_btn.setCursor(Qt.PointingHandCursor)
        infrared_video_btn.mousePressEvent = lambda event: self.select_file("infrared_video")
        
        self.file_selection_layout.addWidget(QLabel("可见光图像:"), 0, 0)
        self.file_selection_layout.addWidget(visible_btn, 0, 1)
        self.file_selection_layout.addWidget(self.visible_path_label, 0, 2)
        
        self.file_selection_layout.addWidget(QLabel("红外图像:"), 1, 0)
        self.file_selection_layout.addWidget(infrared_btn, 1, 1)
        self.file_selection_layout.addWidget(self.infrared_path_label, 1, 2)
        
        self.file_selection_layout.addWidget(QLabel("SAR图像:"), 2, 0)
        self.file_selection_layout.addWidget(sar_btn, 2, 1)
        self.file_selection_layout.addWidget(self.sar_path_label, 2, 2)
        
        self.file_selection_layout.addWidget(QLabel("可见光视频:"), 3, 0)
        self.file_selection_layout.addWidget(visible_video_btn, 3, 1)
        self.file_selection_layout.addWidget(self.visible_video_label, 3, 2)
        
        self.file_selection_layout.addWidget(QLabel("红外视频:"), 4, 0)
        self.file_selection_layout.addWidget(infrared_video_btn, 4, 1)
        self.file_selection_layout.addWidget(self.infrared_video_label, 4, 2)
        
        main_layout.addWidget(self.file_selection_group)
        
        # 视频控制按钮
        self.video_control_layout = QHBoxLayout()
        
        self.play_btn = QPushButton("播放")
        self.play_btn.clicked.connect(self.play_video)
        self.pause_btn = QPushButton("暂停")
        self.pause_btn.clicked.connect(self.pause_video)
        self.stop_btn = QPushButton("停止")
        self.stop_btn.clicked.connect(self.stop_video)
        
        self.video_progress = QSlider(Qt.Horizontal)
        self.video_progress.setRange(0, 1000)
        self.video_progress.sliderMoved.connect(self.seek_video)
        
        self.video_control_layout.addWidget(self.play_btn)
        self.video_control_layout.addWidget(self.pause_btn)
        self.video_control_layout.addWidget(self.stop_btn)
        self.video_control_layout.addWidget(self.video_progress)
        
        main_layout.addLayout(self.video_control_layout)
        self.video_control_layout.setVisible(False)  # 默认隐藏
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setValue(0)
        self.status_label = QLabel("等待选择文件...")
        progress_layout = QHBoxLayout()
        progress_layout.addWidget(self.progress_bar)
        progress_layout.addWidget(self.status_label)
        main_layout.addLayout(progress_layout)
        
        # 结果显示区域 - 使用TabWidget
        self.result_tabs = QTabWidget()
        
        # 原始图像标签
        self.original_image_label = QLabel("原始图像")
        self.original_image_label.setAlignment(Qt.AlignCenter)
        self.original_image_label.setMinimumSize(400, 300)
        self.original_image_label.setStyleSheet("border: 1px solid #a0a0a0;")
        
        # 特征可视化标签
        self.feature_image_label = QLabel("特征可视化")
        self.feature_image_label.setAlignment(Qt.AlignCenter)
        self.feature_image_label.setMinimumSize(400, 300)
        self.feature_image_label.setStyleSheet("border: 1px solid #a0a0a0;")
        
        # 特征信息文本
        self.feature_text = QTextEdit()
        self.feature_text.setReadOnly(True)
        self.feature_text.setMinimumHeight(150)
        
        # 场景识别结果
        self.scene_result_label = QLabel("场景识别结果将显示在这里")
        self.scene_result_label.setAlignment(Qt.AlignCenter)
        self.scene_result_label.setStyleSheet("font-size: 16px; font-weight: bold; padding: 10px;")
        
        # 场景识别依据
        self.scene_reason_text = QTextEdit()
        self.scene_reason_text.setReadOnly(True)
        self.scene_reason_text.setMinimumHeight(100)
        
        # 原始图像和特征可视化布局
        original_feature_layout = QHBoxLayout()
        original_feature_layout.addWidget(self.original_image_label)
        original_feature_layout.addWidget(self.feature_image_label)
        
        # 场景识别布局
        scene_layout = QVBoxLayout()
        scene_layout.addWidget(QLabel("场景识别:"))
        scene_layout.addWidget(self.scene_result_label)
        scene_layout.addWidget(QLabel("判断依据:"))
        scene_layout.addWidget(self.scene_reason_text)
        
        # 特征标签页布局
        feature_tab_layout = QVBoxLayout()
        feature_tab_layout.addLayout(original_feature_layout)
        feature_tab_layout.addLayout(scene_layout)
        feature_tab_layout.addWidget(QLabel("提取的特征:"))
        feature_tab_layout.addWidget(self.feature_text)
        
        feature_tab_widget = QWidget()
        feature_tab_widget.setLayout(feature_tab_layout)
        self.result_tabs.addTab(feature_tab_widget, "特征分析")
        
        # 识别结果标签页
        self.detection_before_label = QLabel("策略调整前")
        self.detection_before_label.setAlignment(Qt.AlignCenter)
        self.detection_before_label.setMinimumSize(400, 300)
        self.detection_before_label.setStyleSheet("border: 1px solid #a0a0a0;")
        
        self.detection_after_label = QLabel("策略调整后")
        self.detection_after_label.setAlignment(Qt.AlignCenter)
        self.detection_after_label.setMinimumSize(400, 300)
        self.detection_after_label.setStyleSheet("border: 1px solid #a0a0a0;")
        
        # 检测信息文本
        self.detection_info_text = QTextEdit()
        self.detection_info_text.setReadOnly(True)
        self.detection_info_text.setMinimumHeight(150)
        
        # 识别结果布局
        detection_layout = QHBoxLayout()
        detection_layout.addWidget(self.detection_before_label)
        detection_layout.addWidget(self.detection_after_label)
        
        # 检测信息布局
        detection_info_layout = QVBoxLayout()
        detection_info_layout.addWidget(QLabel("识别结果信息:"))
        detection_info_layout.addWidget(self.detection_info_text)
        
        # 识别结果标签页布局
        detection_tab_layout = QVBoxLayout()
        detection_tab_layout.addLayout(detection_layout)
        detection_tab_layout.addLayout(detection_info_layout)
        
        detection_tab_widget = QWidget()
        detection_tab_widget.setLayout(detection_tab_layout)
        self.result_tabs.addTab(detection_tab_widget, "识别结果")
        
        # 详细分析标签页
        self.detail_original_label = QLabel("原始锚框区域")
        self.detail_original_label.setAlignment(Qt.AlignCenter)
        self.detail_original_label.setMinimumSize(300, 200)
        self.detail_original_label.setStyleSheet("border: 1px solid #a0a0a0;")
        
        self.detail_infrared_label = QLabel("红外锚框区域")
        self.detail_infrared_label.setAlignment(Qt.AlignCenter)
        self.detail_infrared_label.setMinimumSize(300, 200)
        self.detail_infrared_label.setStyleSheet("border: 1px solid #a0a0a0;")
        
        self.detail_gradient_label = QLabel("梯度特征")
        self.detail_gradient_label.setAlignment(Qt.AlignCenter)
        self.detail_gradient_label.setMinimumSize(300, 200)
        self.detail_gradient_label.setStyleSheet("border: 1px solid #a0a0a0;")
        
        # 详细分析信息文本
        self.detail_info_text = QTextEdit()
        self.detail_info_text.setReadOnly(True)
        self.detail_info_text.setMinimumHeight(100)
        
        # 详细分析布局
        detail_layout = QHBoxLayout()
        detail_layout.addWidget(self.detail_original_label)
        detail_layout.addWidget(self.detail_infrared_label)
        detail_layout.addWidget(self.detail_gradient_label)
        
        # 详细分析信息布局
        detail_info_layout = QVBoxLayout()
        detail_info_layout.addWidget(QLabel("详细分析信息:"))
        detail_info_layout.addWidget(self.detail_info_text)
        
        # 详细分析标签页布局
        detail_tab_layout = QVBoxLayout()
        detail_tab_layout.addLayout(detail_layout)
        detail_tab_layout.addLayout(detail_info_layout)
        
        detail_tab_widget = QWidget()
        detail_tab_widget.setLayout(detail_tab_layout)
        self.result_tabs.addTab(detail_tab_widget, "详细分析")
        
        main_layout.addWidget(self.result_tabs)
        
        # 状态栏
        self.statusBar().showMessage('就绪')
        
        # 自动处理计时器
        self.process_timer = QTimer()
        self.process_timer.timeout.connect(self.auto_process)
        self.process_timer.start(1000)  # 每秒检查一次
        
        # 视频相关属性
        self.visible_cap = None
        self.infrared_cap = None
        self.current_frame = 0
        self.total_frames = 0
        self.is_playing = False
        
        # 初始显示图像选择控件
        self.change_mode(0)
    
    def change_mode(self, index):
        self.is_video_analysis = index == 1
        
        # 显示或隐藏相关控件
        self.visible_path_label.setVisible(not self.is_video_analysis)
        self.infrared_path_label.setVisible(not self.is_video_analysis)
        self.visible_video_label.setVisible(self.is_video_analysis)
        self.infrared_video_label.setVisible(self.is_video_analysis)
        
        # 重置当前选择
        if self.is_video_analysis:
            self.visible_image = None
            self.infrared_image = None
        else:
            self.visible_video_path = None
            self.infrared_video_path = None
            if self.visible_cap:
                self.visible_cap.release()
                self.visible_cap = None
            if self.infrared_cap:
                self.infrared_cap.release()
                self.infrared_cap = None
        
        self.video_control_layout.setVisible(self.is_video_analysis)
    
    def select_file(self, file_type):
        if file_type == "visible_video" or file_type == "infrared_video":
            file_path, _ = QFileDialog.getOpenFileName(
                self, 
                f"选择{'可见光' if file_type == 'visible_video' else '红外'}视频", 
                "", 
                "视频文件 (*.mp4 *.avi *.mov)"
            )
            if file_path:
                if file_type == "visible_video":
                    self.visible_video_path = file_path
                    self.visible_video_label.setText(os.path.basename(file_path))
                    self.visible_cap = cv2.VideoCapture(file_path)
                    self.total_frames = int(self.visible_cap.get(cv2.CAP_PROP_FRAME_COUNT))
                else:
                    self.infrared_video_path = file_path
                    self.infrared_video_label.setText(os.path.basename(file_path))
                    self.infrared_cap = cv2.VideoCapture(file_path)
        else:
            file_path, _ = QFileDialog.getOpenFileName(
                self, 
                f"选择{file_type}图像", 
                "", 
                "图像文件 (*.jpg *.jpeg *.png *.bmp)"
            )
            if file_path:
                image = cv2.imread(file_path)
                if image is not None:
                    if file_type == "visible":
                        self.visible_image = image
                        self.visible_path_label.setText(os.path.basename(file_path))
                    elif file_type == "infrared":
                        self.infrared_image = image
                        self.infrared_path_label.setText(os.path.basename(file_path))
                    elif file_type == "sar":
                        self.sar_image = image
                        self.sar_path_label.setText(os.path.basename(file_path))
                    
                    # 如果选择的是可见光图像，更新原始图像显示
                    if file_type == "visible":
                        self.display_image(image, self.original_image_label)
    
    def auto_process(self):
        # 如果有可见光图像且没有正在运行的处理线程，则开始处理
        if not self.is_video_analysis:
            if self.visible_image is not None and (self.processing_thread is None or not self.processing_thread.isRunning()):
                self.start_processing()
        else:
            # 视频模式下不自动处理
            pass
    
    def start_processing(self, visible_frame=None, infrared_frame=None):
        # 重置UI
        self.progress_bar.setValue(0)
        self.status_label.setText("开始处理...")
        self.feature_text.clear()
        self.scene_result_label.setText("场景识别中...")
        self.scene_reason_text.clear()
        self.detection_info_text.clear()
        
        # 使用当前帧或者默认图像
        visible_to_process = visible_frame if visible_frame is not None else self.visible_image
        infrared_to_process = infrared_frame if infrared_frame is not None else self.infrared_image
        
        # 创建并启动处理线程
        self.processing_thread = ProcessingThread(
            visible_to_process, 
            infrared_to_process, 
            self.sar_image, 
            None  # 不使用视频路径
        )
        self.processing_thread.progress_updated.connect(self.update_progress)
        self.processing_thread.feature_extracted.connect(self.update_features)
        self.processing_thread.scene_recognized.connect(self.update_scene)
        self.processing_thread.detection_updated.connect(self.update_detection)
        self.processing_thread.processing_complete.connect(self.processing_done)
        self.processing_thread.start()
    
    # 视频处理相关方法
    def play_video(self):
        if not self.is_playing and self.visible_cap:
            self.is_playing = True
            self.video_timer.start(30)  # 约30fps
    
    def pause_video(self):
        self.is_playing = False
        self.video_timer.stop()
    
    def stop_video(self):
        self.is_playing = False
        self.video_timer.stop()
        if self.visible_cap:
            self.visible_cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
        self.current_frame = 0
        self.video_progress.setValue(0)
    
    def seek_video(self, position):
        if self.visible_cap:
            frame_pos = int(position * self.total_frames / 1000)
            self.visible_cap.set(cv2.CAP_PROP_POS_FRAMES, frame_pos)
            self.infrared_cap.set(cv2.CAP_PROP_POS_FRAMES, frame_pos)
            self.current_frame = frame_pos
    
    def process_video_frame(self):
        if not self.visible_cap:
            return
        
        # 读取当前帧
        ret_visible, visible_frame = self.visible_cap.read()
        ret_infrared, infrared_frame = self.infrared_cap.read() if self.infrared_cap else (False, None)
        
        if not ret_visible:
            self.stop_video()
            return
        
        self.current_frame = int(self.visible_cap.get(cv2.CAP_PROP_POS_FRAMES))
        self.video_progress.setValue(int(self.current_frame * 1000 / self.total_frames))
        
        # 显示当前帧
        self.display_image(visible_frame, self.original_image_label)
        
        # 处理当前帧
        self.start_processing(visible_frame, infrared_frame)
    
    # 其他方法保持不变...
    def update_progress(self, value, status):
        self.progress_bar.setValue(value)
        self.status_label.setText(status)
        self.statusBar().showMessage(status)
    
    def update_features(self, features):
        # 更新特征文本
        feature_text = "提取的特征:\n\n"
        feature_text += "空域特征:\n"
        for key, value in features['spatial'].items():
            feature_text += f"  {key}: {value}\n"
        
        feature_text += "\n频域特征:\n"
        for key, value in features['frequency'].items():
            feature_text += f"  {key}: {value}\n"
        
        feature_text += "\n小波域特征:\n"
        for key, value in features['wavelet'].items():
            feature_text += f"  {key}: {value}\n"
        
        self.feature_text.setText(feature_text)
        
        # 可视化特征 - 创建一个简单的特征可视化图像
        feature_vis = np.zeros((300, 600, 3), dtype=np.uint8)
        
        # 绘制空域特征条形图
        y_pos = 50
        for i, (key, value) in enumerate(features['spatial'].items()):
            bar_length = int(value * 500)
            cv2.rectangle(feature_vis, (50, y_pos - 10), (50 + bar_length, y_pos + 10), (0, 255, 0), -1)
            cv2.putText(feature_vis, f"{key}: {value}", (550, y_pos + 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            y_pos += 30
        
        # 绘制频域特征条形图
        y_pos = 150
        for i, (key, value) in enumerate(features['frequency'].items()):
            bar_length = int(value * 500)
            cv2.rectangle(feature_vis, (50, y_pos - 10), (50 + bar_length, y_pos + 10), (0, 0, 255), -1)
            cv2.putText(feature_vis, f"{key}: {value}", (550, y_pos + 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            y_pos += 30
        
        # 绘制小波域特征条形图
        y_pos = 250
        for i, (key, value) in enumerate(features['wavelet'].items()):
            bar_length = int(value * 500)
            cv2.rectangle(feature_vis, (50, y_pos - 10), (50 + bar_length, y_pos + 10), (255, 0, 0), -1)
            cv2.putText(feature_vis, f"{key}: {value}", (550, y_pos + 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            y_pos += 30
        
        self.display_image(feature_vis, self.feature_image_label)
    
    def update_scene(self, scene, reason):
        self.current_scene = scene
        self.scene_result_label.setText(f"场景识别结果: {scene}")
        self.scene_reason_text.setText(reason)
        
        # 根据场景设置不同的背景颜色
        if scene == "白天":
            self.scene_result_label.setStyleSheet("font-size: 16px; font-weight: bold; padding: 10px; background-color: #ffffcc;")
        else:  # 黑夜
            self.scene_result_label.setStyleSheet("font-size: 16px; font-weight: bold; padding: 10px; background-color: #333333; color: white;")
    
    def update_detection(self, visible_detections, infrared_detections):
        # visible_detections 和 infrared_detections 是两个列表，分别包含调整前和调整后的检测结果
        
        # 可视化调整前的检测结果
        if self.visible_image is not None:
            before_img = self.visible_image.copy()
            self.draw_detections(before_img, visible_detections[0])  # 调整前的结果
            self.display_image(before_img, self.detection_before_label)
        
        # 可视化调整后的检测结果
        if self.visible_image is not None:
            after_img = self.visible_image.copy()
            self.draw_detections(after_img, visible_detections[1])  # 调整后的结果
            self.display_image(after_img, self.detection_after_label)
        
        # 更新检测信息文本
        detection_info = "识别结果信息:\n\n"
        detection_info += "调整前的识别结果:\n"
        for det in visible_detections[0]:
            detection_info += f"  {det['class']} (置信度: {det['confidence']:.2f}, 位置: {det['box']})\n"
        
        detection_info += "\n调整后的识别结果:\n"
        for det in visible_detections[1]:
            detection_info += f"  {det['class']} (置信度: {det['confidence']:.2f}, 位置: {det['box']})\n"
        
        self.detection_info_text.setText(detection_info)
        
        # 显示详细分析
        if visible_detections[1] and self.visible_image is not None:
            # 选择第一个检测结果进行详细分析
            selected_det = visible_detections[1][0]
            x1, y1, x2, y2 = selected_det['box']
            
            # 提取锚框区域
            roi = self.visible_image[y1:y2, x1:x2]
            if roi.size > 0:
                self.display_image(roi, self.detail_original_label)
                
                # 提取梯度特征
                feature_extractor = FeatureExtractor()
                gradient = feature_extractor.extract_gradient_features(roi)
                self.display_image(gradient, self.detail_gradient_label)
                
                # 如果有红外图像，提取对应区域
                if self.infrared_image is not None and y2 <= self.infrared_image.shape[0] and x2 <= self.infrared_image.shape[1]:
                    if self.infrared_image is not None and y2 <= self.infrared_image.shape[0] and x2 <= self.infrared_image.shape[1]:
                    infrared_roi = self.infrared_image[y1:y2, x1:x2]
                    if infrared_roi.size > 0:
                        self.display_image(infrared_roi, self.detail_infrared_label)
                        
                        # 更新详细分析信息
                        detail_info = "详细分析信息:\n\n"
                        detail_info += f"选择的目标: {selected_det['class']} (置信度: {selected_det['confidence']:.2f})\n\n"
                        
                        # 提取并分析目标特征
                        visible_roi_features = FeatureExtractor().extract_spatial_features(roi)
                        infrared_roi_features = FeatureExtractor().extract_spatial_features(infrared_roi)
                        
                        detail_info += "可见光图像目标特征:\n"
                        for key, value in visible_roi_features.items():
                            detail_info += f"  {key}: {value}\n"
                        
                        detail_info += "\n红外图像目标特征:\n"
                        for key, value in infrared_roi_features.items():
                            detail_info += f"  {key}: {value}\n"
                        
                        # 计算特征差异
                        detail_info += "\n特征差异:\n"
                        for key in visible_roi_features:
                            diff = abs(visible_roi_features[key] - infrared_roi_features[key])
                            detail_info += f"  {key}差异: {diff:.2f}\n"
                        
                        self.detail_info_text.setText(detail_info)
            else:
                self.detail_info_text.setText("无法提取目标区域进行详细分析")
    
    def draw_detections(self, image, detections):
        # 为不同的类别分配不同的颜色
        colors = {
            'person': (0, 255, 0),
            'car': (0, 0, 255),
            'truck': (255, 0, 0),
            'bicycle': (0, 255, 255),
            'motorcycle': (255, 0, 255),
            'bus': (255, 255, 0)
        }
        
        # 在图像上绘制检测框和标签
        for detection in detections:
            x1, y1, x2, y2 = detection['box']
            obj_class = detection['class']
            confidence = detection['confidence']
            
            # 确保坐标在图像范围内
            x1 = max(0, int(x1))
            y1 = max(0, int(y1))
            x2 = min(image.shape[1], int(x2))
            y2 = min(image.shape[0], int(y2))
            
            # 获取颜色
            color = colors.get(obj_class, (255, 255, 255))
            
            # 绘制边界框
            cv2.rectangle(image, (x1, y1), (x2, y2), color, 2)
            
            # 绘制标签背景
            label = f"{obj_class}: {confidence:.2f}"
            label_size, _ = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)
            label_w, label_h = label_size
            cv2.rectangle(image, (x1, y1 - label_h - 10), (x1 + label_w, y1), color, -1)
            
            # 绘制标签文本
            cv2.putText(image, label, (x1, y1 - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 2)
    
    def display_image(self, image, label):
        # 转换图像格式以在Qt中显示
        if len(image.shape) == 3:  # 彩色图像
            rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            h, w, ch = rgb_image.shape
            bytes_per_line = ch * w
            q_image = QImage(rgb_image.data, w, h, bytes_per_line, QImage.Format_RGB888)
        else:  # 灰度图像
            h, w = image.shape
            q_image = QImage(image.data, w, h, w, QImage.Format_Grayscale8)
        
        # 缩放图像以适应标签大小
        pixmap = QPixmap.fromImage(q_image)
        scaled_pixmap = pixmap.scaled(
            label.size(), 
            Qt.KeepAspectRatio, 
            Qt.SmoothTransformation
        )
        label.setPixmap(scaled_pixmap)
    
    def processing_done(self):
        self.statusBar().showMessage('处理完成')
        self.status_label.setText('处理完成')
    
    def closeEvent(self, event):
        # 确保在关闭应用程序时停止所有线程
        if self.processing_thread and self.processing_thread.isRunning():
            self.processing_thread.stop()
        
        if self.video_timer.isActive():
            self.video_timer.stop()
        
        if self.visible_cap:
            self.visible_cap.release()
        
        if self.infrared_cap:
            self.infrared_cap.release()
        
        event.accept()

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())  