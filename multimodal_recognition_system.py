import sys
import os
import random
import numpy as np
import cv2
import json
import datetime
from scipy import fftpack
import pywt
from PyQt5.QtWidgets import (QApplication, QMainWindow, QTabWidget, QWidget, QVBoxLayout,
                            QHBoxLayout, QLabel, QPushButton, QFileDialog,
                            QGroupBox, QTextEdit, QProgressBar)
from PyQt5.QtGui import QPixmap, QImage, QFont
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QMutex, QMutexLocker
from ultralytics import YOLO
from typing import List, Dict
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
#显示中文
QFont("SimHei", 10)  # 设置字体为SimHei，大小为10
class DataTypeDetector:
    """数据类型和探测体制识别器"""
    
    @staticmethod
    def detect_data_type(file_path: str) -> Dict:
        """检测数据类型和探测体制"""
        file_ext = os.path.splitext(file_path)[1].lower()
        file_name = os.path.basename(file_path).lower()
        
        # 基于文件名和扩展名的初步判断
        detection_systems = []
        data_type = "unknown"
        
        # 视频文件
        if file_ext in ['.mp4', '.avi', '.mov', '.mkv']:
            data_type = "video"
        # 图像文件
        elif file_ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tif', '.tiff']:
            data_type = "image"
        
        # 基于文件名判断探测体制
        if any(keyword in file_name for keyword in ['visible', 'rgb', 'color']):
            detection_systems.append("visible_light")
        if any(keyword in file_name for keyword in ['thermal', 'infrared', 'ir']):
            detection_systems.append("thermal_infrared")
        if any(keyword in file_name for keyword in ['sar', 'radar']):
            detection_systems.append("sar")
        
        # 如果无法从文件名判断，尝试从图像内容判断
        if not detection_systems and data_type == "image":
            detection_systems = DataTypeDetector._analyze_image_content(file_path)
        
        return {
            'data_type': data_type,
            'detection_systems': detection_systems,
            'file_path': file_path,
            'file_name': os.path.basename(file_path)
        }
    
    @staticmethod
    def _analyze_image_content(file_path: str) -> List[str]:
        """通过图像内容分析探测体制"""
        try:
            img = cv2.imread(file_path)
            if img is None:
                return ["unknown"]
            
            # 转换为灰度图
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            
            # 分析图像特征
            mean_intensity = np.mean(gray)
            std_intensity = np.std(gray)
            
            # 基于统计特征的简单判断
            detection_systems = []
            
            # 可见光图像通常有较高的对比度和丰富的纹理
            if std_intensity > 30:
                detection_systems.append("visible_light")
            
            # 红外图像通常对比度较低，但有特定的温度分布
            if mean_intensity < 100 and std_intensity < 50:
                detection_systems.append("thermal_infrared")
            
            # SAR图像通常有特定的斑点噪声特征
            # 这里使用简化的判断方法
            edges = cv2.Canny(gray, 50, 150)
            edge_density = np.count_nonzero(edges) / (edges.shape[0] * edges.shape[1])
            if edge_density > 0.1:
                detection_systems.append("sar")
            
            return detection_systems if detection_systems else ["unknown"]
            
        except Exception as e:
            print(f"图像内容分析失败: {e}")
            return ["unknown"]

class MultiDomainFeatureExtractor:
    """多域特征提取器，用于从不同类型的图像中提取空域、频域、小波域特征"""

    @staticmethod
    def extract_spatial_features(image: np.ndarray) -> Dict:
        """提取空域特征"""
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image

        # 基本统计特征
        mean_intensity = np.mean(gray)
        std_intensity = np.std(gray)
        variance = np.var(gray)
        skewness = np.mean(((gray - mean_intensity) / std_intensity) ** 3) if std_intensity > 0 else 0
        kurtosis = np.mean(((gray - mean_intensity) / std_intensity) ** 4) if std_intensity > 0 else 0

        # 边缘特征
        edges = cv2.Canny(gray, 50, 150)
        edge_density = np.count_nonzero(edges) / (edges.shape[0] * edges.shape[1])

        # 纹理特征（基于灰度共生矩阵的简化版本）
        diff_h = np.abs(gray[:, 1:].astype(np.float32) - gray[:, :-1].astype(np.float32))
        diff_v = np.abs(gray[1:, :].astype(np.float32) - gray[:-1, :].astype(np.float32))

        contrast = np.mean(diff_h) + np.mean(diff_v)
        homogeneity = 1.0 / (1.0 + contrast)
        energy = np.sum(gray.astype(np.float32) ** 2) / (gray.shape[0] * gray.shape[1])

        return {
            'mean_intensity': mean_intensity,
            'std_intensity': std_intensity,
            'variance': variance,
            'skewness': skewness,
            'kurtosis': kurtosis,
            'edge_density': edge_density,
            'contrast': contrast,
            'homogeneity': homogeneity,
            'energy': energy
        }

    @staticmethod
    def extract_frequency_features(image: np.ndarray) -> Dict:
        """提取频域特征"""
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image

        # 二维傅里叶变换
        f_transform = fftpack.fft2(gray)
        f_shift = fftpack.fftshift(f_transform)
        magnitude_spectrum = np.abs(f_shift)

        # 频域统计特征
        mean_magnitude = np.mean(magnitude_spectrum)
        std_magnitude = np.std(magnitude_spectrum)
        max_magnitude = np.max(magnitude_spectrum)

        # 频域能量分布
        total_energy = np.sum(magnitude_spectrum ** 2)

        # 计算低频、中频、高频能量比例
        h, w = magnitude_spectrum.shape
        center_h, center_w = h // 2, w // 2

        # 低频区域（中心1/4区域）
        low_freq_region = magnitude_spectrum[center_h-h//8:center_h+h//8, center_w-w//8:center_w+w//8]
        low_freq_energy = np.sum(low_freq_region ** 2) / total_energy if total_energy > 0 else 0

        # 高频区域（边缘区域）
        high_freq_mask = np.ones_like(magnitude_spectrum)
        high_freq_mask[center_h-h//4:center_h+h//4, center_w-w//4:center_w+w//4] = 0
        high_freq_energy = np.sum((magnitude_spectrum * high_freq_mask) ** 2) / total_energy if total_energy > 0 else 0

        # 中频能量
        mid_freq_energy = 1.0 - low_freq_energy - high_freq_energy

        return {
            'mean_magnitude': mean_magnitude,
            'std_magnitude': std_magnitude,
            'max_magnitude': max_magnitude,
            'total_energy': total_energy,
            'low_freq_energy_ratio': low_freq_energy,
            'mid_freq_energy_ratio': mid_freq_energy,
            'high_freq_energy_ratio': high_freq_energy,
            'magnitude_spectrum': magnitude_spectrum  # 用于可视化
        }

    @staticmethod
    def extract_wavelet_features(image: np.ndarray) -> Dict:
        """提取小波域特征"""
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image

        # 小波变换（使用Daubechies小波）
        coeffs = pywt.dwt2(gray, 'db4')
        cA, (cH, cV, cD) = coeffs

        # 各子带的统计特征
        # 低频子带（近似系数）
        ca_mean = np.mean(cA)
        ca_std = np.std(cA)
        ca_energy = np.sum(cA ** 2)

        # 水平细节系数
        ch_mean = np.mean(cH)
        ch_std = np.std(cH)
        ch_energy = np.sum(cH ** 2)

        # 垂直细节系数
        cv_mean = np.mean(cV)
        cv_std = np.std(cV)
        cv_energy = np.sum(cV ** 2)

        # 对角细节系数
        cd_mean = np.mean(cD)
        cd_std = np.std(cD)
        cd_energy = np.sum(cD ** 2)

        # 总能量
        total_energy = ca_energy + ch_energy + cv_energy + cd_energy

        return {
            'ca_mean': ca_mean, 'ca_std': ca_std, 'ca_energy': ca_energy,
            'ch_mean': ch_mean, 'ch_std': ch_std, 'ch_energy': ch_energy,
            'cv_mean': cv_mean, 'cv_std': cv_std, 'cv_energy': cv_energy,
            'cd_mean': cd_mean, 'cd_std': cd_std, 'cd_energy': cd_energy,
            'total_energy': total_energy,
            'ca_energy_ratio': ca_energy / total_energy if total_energy > 0 else 0,
            'ch_energy_ratio': ch_energy / total_energy if total_energy > 0 else 0,
            'cv_energy_ratio': cv_energy / total_energy if total_energy > 0 else 0,
            'cd_energy_ratio': cd_energy / total_energy if total_energy > 0 else 0,
            'coeffs': coeffs  # 用于可视化
        }

class SceneRecognizer:
    """场景识别器，用于判断白天/黑夜场景"""
    
    @staticmethod
    def recognize_scene(visible_features: Dict, thermal_features: Dict) -> Dict:
        """基于可见光和红外特征差异进行场景识别"""
        
        # 提取关键特征
        visible_mean = visible_features.get('mean_intensity', 0)
        visible_std = visible_features.get('std_intensity', 0)
        visible_contrast = visible_features.get('contrast', 0)
        
        thermal_mean = thermal_features.get('mean_intensity', 0)
        thermal_std = thermal_features.get('std_intensity', 0)
        thermal_contrast = thermal_features.get('contrast', 0)
        
        # 计算特征差异
        intensity_diff = abs(visible_mean - thermal_mean)
        contrast_diff = abs(visible_contrast - thermal_contrast)
        std_diff = abs(visible_std - thermal_std)
        
        # 场景判断逻辑
        scene = "unknown"
        confidence = 0.0
        reasons = []
        
        # 白天场景特征：可见光图像亮度高，对比度强
        if visible_mean > 100 and visible_contrast > 20:
            day_score = (visible_mean / 255.0) * 0.4 + (visible_contrast / 100.0) * 0.3 + (visible_std / 100.0) * 0.3
            if day_score > 0.6:
                scene = "day"
                confidence = min(day_score, 1.0)
                reasons.append(f"可见光平均亮度较高({visible_mean:.1f})")
                reasons.append(f"可见光对比度较强({visible_contrast:.1f})")
        
        # 夜晚场景特征：可见光图像亮度低，红外图像相对清晰
        elif visible_mean < 80 and thermal_contrast > visible_contrast:
            night_score = (1.0 - visible_mean / 255.0) * 0.4 + (thermal_contrast / visible_contrast - 1.0) * 0.6
            if night_score > 0.5:
                scene = "night"
                confidence = min(night_score, 1.0)
                reasons.append(f"可见光平均亮度较低({visible_mean:.1f})")
                reasons.append(f"红外对比度({thermal_contrast:.1f})高于可见光对比度({visible_contrast:.1f})")
        
        # 构建判断依据
        judgment_basis = f"因为可见光平均亮度为{visible_mean:.1f}，对比度为{visible_contrast:.1f}；"
        judgment_basis += f"红外平均亮度为{thermal_mean:.1f}，对比度为{thermal_contrast:.1f}；"
        judgment_basis += f"两者亮度差异为{intensity_diff:.1f}，对比度差异为{contrast_diff:.1f}，"
        judgment_basis += f"所以判断为{scene}场景"
        
        return {
            'scene': scene,
            'confidence': confidence,
            'judgment_basis': judgment_basis,
            'reasons': reasons,
            'feature_differences': {
                'intensity_diff': intensity_diff,
                'contrast_diff': contrast_diff,
                'std_diff': std_diff
            }
        }

class ProcessDataManager:
    """过程数据管理器，负责实时保存处理过程数据"""
    
    def __init__(self, base_dir: str = "process_data"):
        self.base_dir = base_dir
        self.session_id = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        self.session_dir = os.path.join(base_dir, f"session_{self.session_id}")
        os.makedirs(self.session_dir, exist_ok=True)
        
        self.process_log = []
        self.mutex = QMutex()
    
    def log_process_step(self, step_name: str, data: Dict, save_immediately: bool = True):
        """记录处理步骤"""
        with QMutexLocker(self.mutex):
            timestamp = datetime.datetime.now().isoformat()
            log_entry = {
                'timestamp': timestamp,
                'step_name': step_name,
                'data': data
            }
            self.process_log.append(log_entry)
            
            if save_immediately:
                self._save_log()
    
    def _save_log(self):
        """保存日志到文件"""
        log_file = os.path.join(self.session_dir, "process_log.json")
        try:
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(self.process_log, f, ensure_ascii=False, indent=2, default=str)
        except Exception as e:
            print(f"保存日志失败: {e}")
    
    def save_image(self, image: np.ndarray, name: str) -> str:
        """保存图像"""
        image_path = os.path.join(self.session_dir, f"{name}.jpg")
        cv2.imwrite(image_path, image)
        return image_path
    
    def get_session_dir(self) -> str:
        """获取会话目录"""
        return self.session_dir

class DetectionSystemManager:
    """探测体制管理器，负责管理不同探测体制的识别模型和权重分配"""

    def __init__(self):
        self.detection_models = {
            'visible_light': {
                'model_path': 'yolo11n.pt',
                'weight': 1.0,
                'description': '可见光探测体制'
            },
            'thermal_infrared': {
                'model_path': 'yolo11n.pt',  # 实际应用中应使用专门的红外模型
                'weight': 1.0,
                'description': '红外探测体制'
            },
            'sar': {
                'model_path': 'yolo11n.pt',  # 实际应用中应使用专门的SAR模型
                'weight': 1.0,
                'description': 'SAR探测体制'
            }
        }
        self.loaded_models = {}

    def get_detection_strategy(self, detection_systems: List[str], scene: str) -> Dict:
        """根据探测体制和场景获取检测策略"""
        if len(detection_systems) == 1:
            strategy = "单探测体制识别"
        elif len(detection_systems) > 1:
            strategy = "特征融合识别策略"  # 或者 "决策融合识别策略"
        else:
            strategy = "未知策略"

        # 根据场景调整权重
        weights = self._calculate_scene_weights(detection_systems, scene)

        return {
            'strategy': strategy,
            'detection_systems': detection_systems,
            'weights': weights,
            'scene': scene
        }

    def _calculate_scene_weights(self, detection_systems: List[str], scene: str) -> Dict:
        """根据场景计算各探测体制的权重"""
        weights = {}

        for system in detection_systems:
            base_weight = self.detection_models.get(system, {}).get('weight', 1.0)

            # 根据场景调整权重
            if scene == "day":
                if system == "visible_light":
                    weights[system] = base_weight * 1.2  # 白天可见光权重增加
                elif system == "thermal_infrared":
                    weights[system] = base_weight * 0.8  # 白天红外权重降低
                else:
                    weights[system] = base_weight
            elif scene == "night":
                if system == "visible_light":
                    weights[system] = base_weight * 0.6  # 夜晚可见光权重降低
                elif system == "thermal_infrared":
                    weights[system] = base_weight * 1.3  # 夜晚红外权重增加
                else:
                    weights[system] = base_weight
            else:
                weights[system] = base_weight

        # 归一化权重
        total_weight = sum(weights.values())
        if total_weight > 0:
            weights = {k: v / total_weight for k, v in weights.items()}

        return weights

    def load_model(self, system: str) -> YOLO:
        """加载指定探测体制的YOLO模型"""
        if system not in self.loaded_models:
            model_path = self.detection_models.get(system, {}).get('model_path', 'yolo11n.pt')
            try:
                self.loaded_models[system] = YOLO(model_path)
                print(f"成功加载{system}模型: {model_path}")
            except Exception as e:
                print(f"加载{system}模型失败: {e}")
                return None

        return self.loaded_models.get(system)

class YOLODetectionProcessor:
    """YOLO检测处理器，负责目标检测和策略调整"""

    def __init__(self, detection_manager: DetectionSystemManager):
        self.detection_manager = detection_manager

    def perform_detection(self, image: np.ndarray, detection_systems: List[str], weights: Dict) -> Dict:
        """执行目标检测"""
        all_detections = []

        for system in detection_systems:
            model = self.detection_manager.load_model(system)
            if model is None:
                continue

            try:
                results = model(image)
                system_detections = self._extract_detections(results, system, weights.get(system, 1.0))
                all_detections.extend(system_detections)
            except Exception as e:
                print(f"{system}检测失败: {e}")

        return {
            'raw_detections': all_detections,
            'adjusted_detections': self._apply_strategy_adjustments(all_detections)
        }

    def _extract_detections(self, results, system: str, weight: float) -> List[Dict]:
        """从YOLO结果中提取检测信息"""
        detections = []

        for result in results:
            boxes = result.boxes
            if boxes is not None:
                for i in range(len(boxes)):
                    detection = {
                        'system': system,
                        'bbox': boxes.xyxy[i].cpu().numpy().tolist(),
                        'confidence': float(boxes.conf[i].cpu().numpy()),
                        'class_id': int(boxes.cls[i].cpu().numpy()),
                        'class_name': result.names[int(boxes.cls[i].cpu().numpy())],
                        'weight': weight
                    }
                    detections.append(detection)

        return detections

    def _apply_strategy_adjustments(self, detections: List[Dict]) -> List[Dict]:
        """应用策略调整（锚框偏移和置信度调整）"""
        adjusted_detections = []

        for detection in detections:
            # 原始检测结果
            original = detection.copy()
            original['stage'] = 'before_adjustment'

            # 应用调整
            adjusted = detection.copy()
            adjusted['stage'] = 'after_adjustment'

            # 锚框偏移调整（示例：随机偏移）
            bbox = adjusted['bbox']
            offset_x = random.uniform(-5, 5)
            offset_y = random.uniform(-5, 5)
            adjusted['bbox'] = [
                bbox[0] + offset_x, bbox[1] + offset_y,
                bbox[2] + offset_x, bbox[3] + offset_y
            ]
            adjusted['bbox_offset'] = [offset_x, offset_y]

            # 置信度调整
            confidence_adjustment = random.uniform(-0.1, 0.1)
            adjusted['confidence'] = max(0.0, min(1.0, adjusted['confidence'] + confidence_adjustment))
            adjusted['confidence_adjustment'] = confidence_adjustment

            adjusted_detections.extend([original, adjusted])

        return adjusted_detections

class GradientFeatureExtractor:
    """梯度特征提取器，专门用于红外图像的梯度特征显示"""

    @staticmethod
    def extract_gradient_features(image: np.ndarray, bbox: List[float]) -> Dict:
        """提取指定区域的梯度特征"""
        # 提取锚框区域
        x1, y1, x2, y2 = [int(coord) for coord in bbox]
        roi = image[y1:y2, x1:x2]

        if len(roi.shape) == 3:
            gray_roi = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
        else:
            gray_roi = roi

        # 计算梯度
        grad_x = cv2.Sobel(gray_roi, cv2.CV_64F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(gray_roi, cv2.CV_64F, 0, 1, ksize=3)

        # 梯度幅值和方向
        magnitude = np.sqrt(grad_x**2 + grad_y**2)
        direction = np.arctan2(grad_y, grad_x)

        # 梯度统计特征
        grad_mean = np.mean(magnitude)
        grad_std = np.std(magnitude)
        grad_max = np.max(magnitude)

        # 梯度方向直方图
        direction_hist, _ = np.histogram(direction, bins=8, range=(-np.pi, np.pi))

        return {
            'roi': roi,
            'gradient_x': grad_x,
            'gradient_y': grad_y,
            'magnitude': magnitude,
            'direction': direction,
            'grad_mean': grad_mean,
            'grad_std': grad_std,
            'grad_max': grad_max,
            'direction_histogram': direction_hist.tolist(),
            'bbox': bbox
        }

class ProcessingThread(QThread):
    """处理线程，用于多线程优化"""

    # 信号定义
    progress_updated = pyqtSignal(str, int)  # 步骤名称, 进度百分比
    step_completed = pyqtSignal(str, dict)   # 步骤名称, 结果数据
    processing_finished = pyqtSignal(dict)   # 最终结果
    error_occurred = pyqtSignal(str)         # 错误信息

    def __init__(self, file_groups: Dict[str, List[str]], parent=None):
        super().__init__(parent)
        self.file_groups = file_groups  # {'visible': [], 'thermal': [], 'sar': []}
        self.data_manager = ProcessDataManager()
        self.detection_manager = DetectionSystemManager()
        self.yolo_processor = YOLODetectionProcessor(self.detection_manager)
        self.is_running = True

    def run(self):
        """主处理流程"""
        try:
            total_steps = 8
            current_step = 0

            # 处理可见光和红外图像对
            visible_files = self.file_groups.get('visible', [])
            thermal_files = self.file_groups.get('thermal', [])
            sar_files = self.file_groups.get('sar', [])
            
            # 配对处理可见光和红外图像
            for i in range(max(len(visible_files), len(thermal_files))):
                if not self.is_running:
                    break
                    
                visible_path = visible_files[i] if i < len(visible_files) else None
                thermal_path = thermal_files[i] if i < len(thermal_files) else None
                
                # 步骤1: 数据类型检测
                current_step += 1
                self.progress_updated.emit("数据类型检测", int(current_step / total_steps * 100))
                data_info = {
                    'visible': DataTypeDetector.detect_data_type(visible_path) if visible_path else None,
                    'thermal': DataTypeDetector.detect_data_type(thermal_path) if thermal_path else None
                }
                self.step_completed.emit("数据类型检测", data_info)
                self.data_manager.log_process_step("数据类型检测", data_info)

                # 加载图像对
                visible_image = cv2.imread(visible_path) if visible_path else None
                thermal_image = cv2.imread(thermal_path) if thermal_path else None
                
                if visible_image is None and thermal_image is None:
                    self.error_occurred.emit("无法加载可见光或红外图像")
                    continue

                # 步骤2: 多域特征提取
                current_step += 1
                self.progress_updated.emit("多域特征提取", int(current_step / total_steps * 100))

                # 提取可见光和红外图像特征
                visible_features = {}
                thermal_features = {}
                
                if visible_image is not None:
                    visible_features = {
                        'spatial': MultiDomainFeatureExtractor.extract_spatial_features(visible_image),
                        'frequency': MultiDomainFeatureExtractor.extract_frequency_features(visible_image),
                        'wavelet': MultiDomainFeatureExtractor.extract_wavelet_features(visible_image)
                    }
                
                if thermal_image is not None:
                    thermal_features = {
                        'spatial': MultiDomainFeatureExtractor.extract_spatial_features(thermal_image),
                        'frequency': MultiDomainFeatureExtractor.extract_frequency_features(thermal_image),
                        'wavelet': MultiDomainFeatureExtractor.extract_wavelet_features(thermal_image)
                    }

                features_data = {
                    'visible': visible_features,
                    'thermal': thermal_features
                }
                self.step_completed.emit("多域特征提取", features_data)
                self.data_manager.log_process_step("多域特征提取", features_data)

                # 步骤3: 场景识别（需要可见光和红外图像）
                current_step += 1
                self.progress_updated.emit("场景识别", int(current_step / total_steps * 100))

                # 使用真实的可见光和红外特征进行场景识别
                visible_spatial = visible_features.get('spatial', {}) if visible_image else {}
                thermal_spatial = thermal_features.get('spatial', {}) if thermal_image else {}
                scene_result = SceneRecognizer.recognize_scene(visible_spatial, thermal_spatial)
                self.step_completed.emit("场景识别", scene_result)
                self.data_manager.log_process_step("场景识别", scene_result)

                # 步骤4: 探测体制策略确定
                current_step += 1
                self.progress_updated.emit("探测体制策略确定", int(current_step / total_steps * 100))

                strategy_info = self.detection_manager.get_detection_strategy(
                    data_info['detection_systems'],
                    scene_result['scene']
                )
                self.step_completed.emit("探测体制策略确定", strategy_info)
                self.data_manager.log_process_step("探测体制策略确定", strategy_info)

                # 步骤5: YOLO目标检测
                current_step += 1
                self.progress_updated.emit("目标检测", int(current_step / total_steps * 100))

                detection_results = self.yolo_processor.perform_detection(
                    image,
                    data_info['detection_systems'],
                    strategy_info['weights']
                )
                self.step_completed.emit("目标检测", detection_results)
                self.data_manager.log_process_step("目标检测", detection_results)

                # 步骤6: 红外梯度特征提取
                current_step += 1
                self.progress_updated.emit("梯度特征提取", int(current_step / total_steps * 100))

                gradient_features = []
                for detection in detection_results['adjusted_detections']:
                    if detection['stage'] == 'after_adjustment':
                        gradient_feature = GradientFeatureExtractor.extract_gradient_features(
                            image, detection['bbox']
                        )
                        gradient_features.append(gradient_feature)

                gradient_data = {'gradient_features': gradient_features}
                self.step_completed.emit("梯度特征提取", gradient_data)
                self.data_manager.log_process_step("梯度特征提取", gradient_data)

                # 步骤7: 结果可视化准备
                current_step += 1
                self.progress_updated.emit("结果可视化", int(current_step / total_steps * 100))

                # 保存处理过程中的图像
                processed_image_path = self.data_manager.save_image(image, f"processed_{os.path.basename(file_path)}")

                visualization_data = {
                    'original_image_path': file_path,
                    'processed_image_path': processed_image_path,
                    'features_visualized': True
                }
                self.step_completed.emit("结果可视化", visualization_data)
                self.data_manager.log_process_step("结果可视化", visualization_data)

                # 步骤8: 完成处理
                current_step += 1
                self.progress_updated.emit("处理完成", 100)

                final_result = {
                    'file_path': file_path,
                    'data_info': data_info,
                    'features': features_data,
                    'scene_result': scene_result,
                    'strategy_info': strategy_info,
                    'detection_results': detection_results,
                    'gradient_features': gradient_data,
                    'visualization_data': visualization_data,
                    'session_dir': self.data_manager.get_session_dir()
                }

                self.processing_finished.emit(final_result)

        except Exception as e:
            self.error_occurred.emit(f"处理过程中发生错误: {str(e)}")

    def stop(self):
        """停止处理"""
        self.is_running = False

class FeatureVisualizationWidget(QWidget):
    """特征可视化组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.initUI()

    def initUI(self):
        layout = QVBoxLayout(self)

        # 创建matplotlib图形
        self.figure = Figure(figsize=(12, 8))
        self.canvas = FigureCanvas(self.figure)
        layout.addWidget(self.canvas)

        # 特征信息显示
        self.info_text = QTextEdit()
        self.info_text.setMaximumHeight(150)
        layout.addWidget(self.info_text)

    def display_features(self, features_data: Dict):
        """显示特征数据"""
        self.figure.clear()

        # 创建子图
        spatial_features = features_data.get('spatial', {})
        frequency_features = features_data.get('frequency', {})
        wavelet_features = features_data.get('wavelet', {})

        # 空域特征可视化
        ax1 = self.figure.add_subplot(2, 3, 1)
        spatial_values = [spatial_features.get(key, 0) for key in ['mean_intensity', 'std_intensity', 'contrast', 'edge_density']]
        spatial_labels = ['平均强度', '标准差', '对比度', '边缘密度']
        ax1.bar(spatial_labels, spatial_values)
        ax1.set_title('空域特征')
        ax1.tick_params(axis='x', rotation=45)

        # 频域特征可视化
        ax2 = self.figure.add_subplot(2, 3, 2)
        freq_ratios = [
            frequency_features.get('low_freq_energy_ratio', 0),
            frequency_features.get('mid_freq_energy_ratio', 0),
            frequency_features.get('high_freq_energy_ratio', 0)
        ]
        ax2.pie(freq_ratios, labels=['低频', '中频', '高频'], autopct='%1.1f%%')
        ax2.set_title('频域能量分布')

        # 小波域特征可视化
        ax3 = self.figure.add_subplot(2, 3, 3)
        wavelet_ratios = [
            wavelet_features.get('ca_energy_ratio', 0),
            wavelet_features.get('ch_energy_ratio', 0),
            wavelet_features.get('cv_energy_ratio', 0),
            wavelet_features.get('cd_energy_ratio', 0)
        ]
        ax3.pie(wavelet_ratios, labels=['近似', '水平', '垂直', '对角'], autopct='%1.1f%%')
        ax3.set_title('小波域能量分布')

        # 频谱图显示
        if 'magnitude_spectrum' in frequency_features:
            ax4 = self.figure.add_subplot(2, 3, 4)
            spectrum = frequency_features['magnitude_spectrum']
            ax4.imshow(np.log(spectrum + 1), cmap='gray')
            ax4.set_title('频谱图')
            ax4.axis('off')

        # 小波系数显示
        if 'coeffs' in wavelet_features:
            coeffs = wavelet_features['coeffs']
            cA, (cH, cV, cD) = coeffs

            ax5 = self.figure.add_subplot(2, 3, 5)
            ax5.imshow(cA, cmap='gray')
            ax5.set_title('小波近似系数')
            ax5.axis('off')

            ax6 = self.figure.add_subplot(2, 3, 6)
            combined_detail = np.hstack([cH, cV, cD])
            ax6.imshow(combined_detail, cmap='gray')
            ax6.set_title('小波细节系数')
            ax6.axis('off')

        self.figure.tight_layout()
        self.canvas.draw()

        # 更新特征信息文本
        info_text = "特征提取结果:\n\n"
        info_text += "空域特征:\n"
        for key, value in spatial_features.items():
            if isinstance(value, (int, float)):
                info_text += f"  {key}: {value:.4f}\n"

        info_text += "\n频域特征:\n"
        for key, value in frequency_features.items():
            if isinstance(value, (int, float)):
                info_text += f"  {key}: {value:.4f}\n"

        info_text += "\n小波域特征:\n"
        for key, value in wavelet_features.items():
            if isinstance(value, (int, float)):
                info_text += f"  {key}: {value:.4f}\n"

        self.info_text.setText(info_text)

class ImageDisplayWidget(QWidget):
    """图像显示组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.initUI()
        self.current_image = None
        self.detections = []

    def initUI(self):
        layout = QVBoxLayout(self)

        # 图像显示标签
        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setStyleSheet("border: 1px solid gray;")
        self.image_label.setMinimumSize(400, 300)
        layout.addWidget(self.image_label)

        # 图像信息
        self.info_label = QLabel("未加载图像")
        layout.addWidget(self.info_label)

    def display_image(self, image_path: str, detections: List[Dict] = None):
        """显示图像和检测结果"""
        try:
            # 加载图像
            image = cv2.imread(image_path)
            if image is None:
                self.info_label.setText("无法加载图像")
                return

            self.current_image = image.copy()
            self.detections = detections or []

            # 绘制检测框
            display_image = image.copy()
            if detections:
                for detection in detections:
                    bbox = detection['bbox']
                    confidence = detection['confidence']
                    class_name = detection.get('class_name', 'unknown')
                    stage = detection.get('stage', 'unknown')

                    # 根据阶段选择颜色
                    if stage == 'before_adjustment':
                        color = (0, 0, 255)  # 红色 - 调整前
                        label = f"{class_name} (调整前): {confidence:.2f}"
                    else:
                        color = (0, 255, 0)  # 绿色 - 调整后
                        label = f"{class_name} (调整后): {confidence:.2f}"

                    # 绘制边界框
                    x1, y1, x2, y2 = [int(coord) for coord in bbox]
                    cv2.rectangle(display_image, (x1, y1), (x2, y2), color, 2)

                    # 绘制标签
                    label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]
                    cv2.rectangle(display_image, (x1, y1 - label_size[1] - 10),
                                (x1 + label_size[0], y1), color, -1)
                    cv2.putText(display_image, label, (x1, y1 - 5),
                              cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)

            # 转换为Qt格式并显示
            height, width, _ = display_image.shape
            bytes_per_line = 3 * width
            q_image = QImage(display_image.data, width, height, bytes_per_line, QImage.Format_RGB888).rgbSwapped()

            # 缩放图像以适应显示区域
            pixmap = QPixmap.fromImage(q_image)
            scaled_pixmap = pixmap.scaled(self.image_label.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation)
            self.image_label.setPixmap(scaled_pixmap)

            # 更新信息
            info_text = f"图像: {os.path.basename(image_path)}\n"
            info_text += f"尺寸: {width} x {height}\n"
            if detections:
                info_text += f"检测到 {len(detections)} 个目标"
            self.info_label.setText(info_text)

        except Exception as e:
            self.info_label.setText(f"显示图像失败: {e}")

class GradientVisualizationWidget(QWidget):
    """梯度特征可视化组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.initUI()

    def initUI(self):
        layout = QVBoxLayout(self)

        # 创建matplotlib图形
        self.figure = Figure(figsize=(10, 6))
        self.canvas = FigureCanvas(self.figure)
        layout.addWidget(self.canvas)

        # 梯度信息显示
        self.gradient_info = QTextEdit()
        self.gradient_info.setMaximumHeight(100)
        layout.addWidget(self.gradient_info)

    def display_gradient_features(self, gradient_features: List[Dict]):
        """显示梯度特征"""
        if not gradient_features:
            return

        self.figure.clear()

        # 显示前几个检测区域的梯度特征
        num_features = min(len(gradient_features), 4)

        for i, feature in enumerate(gradient_features[:num_features]):
            # ROI区域
            ax1 = self.figure.add_subplot(2, num_features, i + 1)
            ax1.imshow(feature['roi'], cmap='gray')
            ax1.set_title(f'ROI {i+1}')
            ax1.axis('off')

            # 梯度幅值
            ax2 = self.figure.add_subplot(2, num_features, i + 1 + num_features)
            ax2.imshow(feature['magnitude'], cmap='hot')
            ax2.set_title(f'梯度幅值 {i+1}')
            ax2.axis('off')

        self.figure.tight_layout()
        self.canvas.draw()

        # 更新梯度信息
        info_text = "红外梯度特征分析:\n"
        for i, feature in enumerate(gradient_features[:3]):  # 显示前3个
            info_text += f"区域{i+1}: 平均梯度={feature['grad_mean']:.2f}, "
            info_text += f"最大梯度={feature['grad_max']:.2f}\n"

        self.gradient_info.setText(info_text)

class MultiModalRecognitionMainWindow(QMainWindow):
    """多模态识别系统主窗口"""

    def __init__(self):
        super().__init__()
        self.processing_thread = None
        self.current_results = {}
        self.initUI()

    def initUI(self):
        self.setWindowTitle("多模态识别系统 - 自动处理流程")
        self.setGeometry(100, 100, 1400, 900)

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QHBoxLayout(central_widget)

        # 左侧控制面板
        control_panel = self.create_control_panel()
        main_layout.addWidget(control_panel, 1)

        # 右侧显示区域
        display_area = self.create_display_area()
        main_layout.addWidget(display_area, 3)

        # 状态栏
        self.statusBar().showMessage("就绪")

    def create_control_panel(self) -> QWidget:
        """创建控制面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)

        # 文件选择组
        file_group = QGroupBox("文件选择")
        file_layout = QVBoxLayout(file_group)

        # 文件路径显示
        self.file_path_label = QLabel("未选择文件")
        self.file_path_label.setWordWrap(True)
        file_layout.addWidget(self.file_path_label)

        # 选择文件按钮
        select_btn = QPushButton("选择图像/视频文件")
        select_btn.clicked.connect(self.select_files)
        file_layout.addWidget(select_btn)

        layout.addWidget(file_group)

        # 处理状态组
        status_group = QGroupBox("处理状态")
        status_layout = QVBoxLayout(status_group)

        # 进度条
        self.progress_bar = QProgressBar()
        status_layout.addWidget(self.progress_bar)

        # 当前步骤显示
        self.current_step_label = QLabel("等待开始...")
        status_layout.addWidget(self.current_step_label)

        # 处理日志
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        status_layout.addWidget(self.log_text)

        layout.addWidget(status_group)

        # 结果信息组
        result_group = QGroupBox("识别结果")
        result_layout = QVBoxLayout(result_group)

        self.result_text = QTextEdit()
        self.result_text.setMaximumHeight(150)
        result_layout.addWidget(self.result_text)

        layout.addWidget(result_group)

        # 添加弹性空间
        layout.addStretch()

        return panel

    def create_display_area(self) -> QWidget:
        """创建显示区域"""
        # 创建标签页
        tabs = QTabWidget()

        # 图像显示标签页
        self.image_widget = ImageDisplayWidget()
        tabs.addTab(self.image_widget, "图像显示")

        # 特征可视化标签页
        self.feature_widget = FeatureVisualizationWidget()
        tabs.addTab(self.feature_widget, "特征可视化")

        # 梯度特征标签页
        self.gradient_widget = GradientVisualizationWidget()
        tabs.addTab(self.gradient_widget, "梯度特征")

        return tabs

    def select_files(self):
        """选择多模态文件(可见光/红外/SAR)"""
        options = QFileDialog.Options()
        file_paths, _ = QFileDialog.getOpenFileNames(
            self,
            "选择多模态图像文件(可多选可见光/红外/SAR)",
            "",
            "图像文件 (*.png *.jpg *.jpeg *.bmp *.tif *.tiff);;所有文件 (*)",
            options=options
        )

        if file_paths:
            # 分类文件类型
            visible_files = []
            thermal_files = []
            sar_files = []
            
            for file_path in file_paths:
                data_info = DataTypeDetector.detect_data_type(file_path)
                if 'visible_light' in data_info['detection_systems']:
                    visible_files.append(file_path)
                elif 'thermal_infrared' in data_info['detection_systems']:
                    thermal_files.append(file_path)
                elif 'sar' in data_info['detection_systems']:
                    sar_files.append(file_path)
            
            # 显示选择结果
            info_text = "已选择文件:\n"
            if visible_files:
                info_text += f"可见光图像: {len(visible_files)}个\n"
            if thermal_files:
                info_text += f"红外图像: {len(thermal_files)}个\n"
            if sar_files:
                info_text += f"SAR图像: {len(sar_files)}个\n"
            
            self.file_path_label.setText(info_text)
            
            # 启动处理，传入分类后的文件
            self.start_processing({
                'visible': visible_files,
                'thermal': thermal_files,
                'sar': sar_files
            })

    def start_processing(self, file_paths: List[str]):
        """开始处理"""
        if self.processing_thread and self.processing_thread.isRunning():
            self.processing_thread.stop()
            self.processing_thread.wait()

        # 创建并启动处理线程
        self.processing_thread = ProcessingThread(file_paths)
        self.processing_thread.progress_updated.connect(self.update_progress)
        self.processing_thread.step_completed.connect(self.step_completed)
        self.processing_thread.processing_finished.connect(self.processing_finished)
        self.processing_thread.error_occurred.connect(self.handle_error)

        self.processing_thread.start()
        self.log_text.append("开始自动处理流程...")

    def update_progress(self, step_name: str, progress: int):
        """更新进度"""
        self.current_step_label.setText(f"当前步骤: {step_name}")
        self.progress_bar.setValue(progress)
        self.statusBar().showMessage(f"正在执行: {step_name} ({progress}%)")

    def step_completed(self, step_name: str, data: Dict):
        """步骤完成"""
        self.log_text.append(f"✓ {step_name} 完成")

        # 根据步骤类型更新显示
        if step_name == "数据类型检测":
            detection_systems = data.get('detection_systems', [])
            strategy_text = "单探测体制识别" if len(detection_systems) == 1 else "特征融合识别策略"
            self.log_text.append(f"  探测体制: {', '.join(detection_systems)}")
            self.log_text.append(f"  识别策略: {strategy_text}")

        elif step_name == "场景识别":
            scene = data.get('scene', 'unknown')
            confidence = data.get('confidence', 0)
            judgment_basis = data.get('judgment_basis', '')
            self.log_text.append(f"  场景: {scene} (置信度: {confidence:.2f})")
            self.log_text.append(f"  判断依据: {judgment_basis}")

        elif step_name == "多域特征提取":
            self.feature_widget.display_features(data)
            self.log_text.append("  特征可视化已更新")

        elif step_name == "目标检测":
            raw_count = len(data.get('raw_detections', []))
            adjusted_count = len(data.get('adjusted_detections', []))
            self.log_text.append(f"  检测到 {raw_count} 个原始目标")
            self.log_text.append(f"  策略调整后 {adjusted_count} 个结果")

        elif step_name == "梯度特征提取":
            gradient_features = data.get('gradient_features', [])
            self.gradient_widget.display_gradient_features(gradient_features)
            self.log_text.append(f"  提取了 {len(gradient_features)} 个区域的梯度特征")

        # 滚动到底部
        self.log_text.verticalScrollBar().setValue(self.log_text.verticalScrollBar().maximum())

    def processing_finished(self, result: Dict):
        """处理完成"""
        self.current_results = result

        # 显示最终结果
        self.display_final_results(result)

        # 更新状态
        self.current_step_label.setText("处理完成")
        self.progress_bar.setValue(100)
        self.statusBar().showMessage("处理完成")
        self.log_text.append("=" * 50)
        self.log_text.append("🎉 所有处理步骤已完成!")
        self.log_text.append(f"结果保存在: {result.get('session_dir', '')}")

    def display_final_results(self, result: Dict):
        """显示最终结果"""
        # 显示图像和检测结果
        file_path = result.get('file_path', '')
        detection_results = result.get('detection_results', {})
        adjusted_detections = detection_results.get('adjusted_detections', [])

        if file_path:
            self.image_widget.display_image(file_path, adjusted_detections)

        # 显示结果摘要
        data_info = result.get('data_info', {})
        scene_result = result.get('scene_result', {})
        strategy_info = result.get('strategy_info', {})

        result_text = "=== 识别结果摘要 ===\n\n"
        result_text += f"文件: {os.path.basename(file_path)}\n"
        result_text += f"数据类型: {data_info.get('data_type', 'unknown')}\n"
        result_text += f"探测体制: {', '.join(data_info.get('detection_systems', []))}\n"
        result_text += f"识别策略: {strategy_info.get('strategy', 'unknown')}\n\n"

        result_text += f"场景识别: {scene_result.get('scene', 'unknown')}\n"
        result_text += f"置信度: {scene_result.get('confidence', 0):.2f}\n"
        result_text += f"判断依据: {scene_result.get('judgment_basis', '')}\n\n"

        # 权重信息
        weights = strategy_info.get('weights', {})
        if weights:
            result_text += "体制权重分配:\n"
            for system, weight in weights.items():
                result_text += f"  {system}: {weight:.3f}\n"

        # 检测统计
        before_count = len([d for d in adjusted_detections if d.get('stage') == 'before_adjustment'])
        after_count = len([d for d in adjusted_detections if d.get('stage') == 'after_adjustment'])
        result_text += f"\n检测结果:\n"
        result_text += f"  策略调整前: {before_count} 个目标\n"
        result_text += f"  策略调整后: {after_count} 个目标\n"

        self.result_text.setText(result_text)

    def handle_error(self, error_message: str):
        """处理错误"""
        self.log_text.append(f"❌ 错误: {error_message}")
        self.current_step_label.setText("处理出错")
        self.statusBar().showMessage("处理出错")

    def closeEvent(self, event):
        """关闭事件"""
        if self.processing_thread and self.processing_thread.isRunning():
            self.processing_thread.stop()
            self.processing_thread.wait()
        event.accept()

def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用程序信息
    app.setApplicationName("多模态识别系统")
    app.setApplicationVersion("1.0")

    # 设置字体
    font = QFont("Microsoft YaHei", 9)
    app.setFont(font)

    # 创建主窗口
    window = MultiModalRecognitionMainWindow()
    window.show()

    # 运行应用程序
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
